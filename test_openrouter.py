#!/usr/bin/env python3
"""
OpenRouter服务测试

测试OpenRouter AI服务的功能
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.services.ai_service import OpenRouterService, AIServiceFactory

# 加载环境变量
load_dotenv()

async def test_openrouter_service():
    """测试OpenRouter服务"""
    print("OpenRouter服务测试")
    print("=" * 50)
    
    # 获取API密钥
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("❌ 请设置OPENROUTER_API_KEY环境变量")
        return False
    
    # 测试不同的模型
    test_models = [
        "openai/gpt-3.5-turbo",
        "anthropic/claude-3-haiku",
        "google/gemini-pro",
        "google/gemini-2.5-flash",
    ]
    
    # 简单的测试试题
    test_text = """
    1. 以下哪个不是Python的基本数据类型？
    A. int
    B. float
    C. string
    D. array
    
    2. 判断题：Python是面向对象的编程语言。
    """
    
    success_count = 0
    total_tests = len(test_models)
    
    for model in test_models:
        print(f"\n🧪 测试模型: {model}")
        print("-" * 30)
        
        try:
            # 创建服务实例
            service = OpenRouterService(
                api_key=api_key,
                model_name=model,
                max_tokens=2000
            )
            
            # 解析试题
            questions, remaining_text = await service.parse_questions_with_remaining(test_text)
            
            if questions:
                print(f"✅ 成功解析 {len(questions)} 道试题")
                
                # 显示第一题的详细信息
                if questions:
                    q = questions[0]
                    print(f"   - 题型: {q.type.value}")
                    print(f"   - 题干: {q.stem[:50]}...")
                    if q.choices:
                        print(f"   - 选项数: {len(q.choices)}")
                    if q.answer:
                        print(f"   - 答案: {q.answer}")
                
                success_count += 1
            else:
                print("⚠️  未解析到试题")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            continue
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 个模型测试成功")
    return success_count > 0


async def test_factory_integration():
    """测试工厂模式集成"""
    print("\n\n工厂模式集成测试")
    print("=" * 50)
    
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("❌ 请设置OPENROUTER_API_KEY环境变量")
        return False
    
    try:
        # 通过工厂创建OpenRouter服务
        service = AIServiceFactory.create_service(
            provider="openrouter",
            api_key=api_key,
            model_name="openai/gpt-3.5-turbo"
        )
        
        print(f"✅ 成功创建服务: {service.__class__.__name__}")
        print(f"   - 模型: {service.model_name}")
        print(f"   - 最大Token: {service.max_tokens}")
        
        # 简单测试
        test_question = "1. Python是什么类型的语言？\nA. 编译型\nB. 解释型\nC. 混合型\nD. 机器语言"
        
        questions = await service.parse_questions(test_question)
        if questions:
            print(f"✅ 解析测试成功，获得 {len(questions)} 道试题")
            return True
        else:
            print("⚠️  解析测试失败，未获得试题")
            return False
            
    except Exception as e:
        print(f"❌ 工厂测试失败: {e}")
        return False


async def test_error_handling():
    """测试错误处理"""
    print("\n\n错误处理测试")
    print("=" * 50)
    
    # 测试无效API密钥
    try:
        service = OpenRouterService(
            api_key="invalid_key",
            model_name="openai/gpt-3.5-turbo"
        )
        
        await service.parse_questions("测试文本")
        print("❌ 应该抛出异常，但没有")
        return False
        
    except Exception as e:
        print(f"✅ 正确处理了无效API密钥错误: {type(e).__name__}")
    
    # 测试无效provider
    try:
        AIServiceFactory.create_service(
            provider="invalid_provider",
            api_key="test_key"
        )
        print("❌ 应该抛出异常，但没有")
        return False
        
    except ValueError as e:
        print(f"✅ 正确处理了无效provider错误: {e}")
        return True
    
    except Exception as e:
        print(f"⚠️  抛出了非预期的异常: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 OpenRouter集成测试开始")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("OpenRouter服务测试", test_openrouter_service),
        ("工厂模式集成测试", test_factory_integration),
        ("错误处理测试", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"\n✅ {test_name} 通过")
            else:
                print(f"\n❌ {test_name} 失败")
        except Exception as e:
            print(f"\n💥 {test_name} 异常: {e}")
    
    print(f"\n🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！OpenRouter集成成功！")
        print("\n💡 使用提示:")
        print("- 设置环境变量: OPENROUTER_API_KEY=your_key")
        print("- 使用示例: python example_openrouter_usage.py")
        print("- API调用: ai_provider='openrouter'")
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main()) 