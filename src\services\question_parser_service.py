"""试题解析服务"""
import os
import uuid
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor

from ..models.question import Question, QuestionSet
from ..parsers.base_parser import BaseParser
from ..parsers.text_parser import TextParser
from ..parsers.pdf_parser import PDFParser
from ..parsers.word_parser import WordParser
from ..parsers.excel_parser import ExcelParser
from .ai_service import AIService, AIServiceFactory


class QuestionParserService:
    """试题解析服务"""
    
    def __init__(self, ai_provider: str, api_key: str, model_name: Optional[str] = None,
                 chunk_size: int = 2000, max_concurrent_requests: int = 5):
        """
        初始化服务
        
        Args:
            ai_provider: AI服务提供商
            api_key: API密钥
            model_name: 模型名称
            chunk_size: 文本块大小
            max_concurrent_requests: 最大并发请求数
        """
        self.ai_service = AIServiceFactory.create_service(ai_provider, api_key, model_name)
        self.chunk_size = chunk_size
        self.max_concurrent_requests = max_concurrent_requests
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 文件解析器映射
        self.parsers = {
            '.txt': TextParser(chunk_size=chunk_size),
            '.pdf': PDFParser(chunk_size=chunk_size),
            '.docx': WordParser(chunk_size=chunk_size),
            '.doc': WordParser(chunk_size=chunk_size),
            '.xlsx': ExcelParser(chunk_size=chunk_size),
            '.xls': ExcelParser(chunk_size=chunk_size),
        }
    
    def get_parser(self, file_path: str) -> Optional[BaseParser]:
        """根据文件扩展名获取解析器"""
        ext = Path(file_path).suffix.lower()
        return self.parsers.get(ext)
    

    
    async def parse_text(self, text: str, 
                        file_name: Optional[str] = None) -> QuestionSet:
        """
        直接解析文本中的试题
        
        Args:
            text: 文本内容
            file_name: 文件名（可选）
            
        Returns:
            试题集
        """
        # 创建临时解析器进行分块
        parser = TextParser(chunk_size=self.chunk_size)
        chunks = parser.split_text_into_chunks(text)
        
        # 处理文本块
        all_questions = []
        for i, chunk in enumerate(chunks):
            try:
                questions = await self.ai_service.parse_questions(
                    chunk['content'],
                    f"文本块 {i+1}/{len(chunks)}"
                )
                
                for q in questions:
                    if not q.id:
                        q.id = str(uuid.uuid4())
                    q.metadata['chunk_index'] = i
                
                all_questions.extend(questions)
            
            except Exception as e:
                print(f"处理文本块{i}失败: {e}")
        
        # 创建试题集
        return QuestionSet(
            title=file_name or "直接输入的文本",
            questions=all_questions,
            metadata={
                'source': 'direct_text',
                'text_length': len(text),
                'total_chunks': len(chunks)
            }
        )
    
    def validate_file(self, file_path: str, max_size: int = 104857600) -> Tuple[bool, str]:
        """
        验证文件
        
        Args:
            file_path: 文件路径
            max_size: 最大文件大小（字节）
            
        Returns:
            (是否有效, 错误信息)
        """
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return False, "文件不存在"
        
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        if file_size > max_size:
            return False, f"文件太大，最大支持{max_size/1024/1024:.1f}MB"
        
        # 检查文件格式
        if not self.get_parser(file_path):
            return False, f"不支持的文件格式: {Path(file_path).suffix}"
        
        return True, ""
    
 

    async def parse_file_dynamic(self, file_path: str, 
                               callback: Optional[callable] = None) -> QuestionSet:
        """
        使用动态分块解析文件中的试题
        
        Args:
            file_path: 文件路径
            callback: 进度回调函数
            
        Returns:
            试题集
        """
        # 获取解析器
        parser = self.get_parser(file_path)
        if not parser:
            raise ValueError(f"不支持的文件格式: {Path(file_path).suffix}")
        
        # 提取完整文本
        loop = asyncio.get_event_loop()
        full_text = await loop.run_in_executor(
            self.executor, parser.extract_text, file_path
        )
        
        if callback:
            callback({
                'status': 'parsing',
                'message': '文件解析完成，开始提取试题',
                'progress': 10
            })
        
        # 动态分块解析
        all_questions = []
        remaining_text = full_text
        chunk_index = 0
        
        while remaining_text.strip():
            # 计算当前进度
            processed_ratio = 1 - (len(remaining_text) / len(full_text))
            
            if callback:
                callback({
                    'status': 'processing',
                    'message': f'正在处理第{chunk_index+1}个文本块',
                    'progress': 10 + (80 * processed_ratio)
                })
            
                        # 动态确定块大小
            chunk_text = self._get_next_chunk(remaining_text, chunk_index)
            
            try:
                # 解析当前块
                questions, unprocessed = await self.ai_service.parse_questions_with_remaining(
                    chunk_text, 
                    f"这是第{chunk_index+1}个文本块"
                )
                
                print('---------------------------unprocessed------------------------------')
                print(unprocessed)
                print('---------------------------unprocessed------------------------------')

                # 为每个问题添加元数据
                for q in questions:
                    q.metadata['chunk_index'] = chunk_index
                    q.metadata['file_name'] = Path(file_path).name
                    if not q.id:
                        q.id = str(uuid.uuid4())
                
                all_questions.extend(questions)
                
                # 更新剩余文本
                if unprocessed:
                    # 找到未处理文本在原始块中的位置
                    unprocessed_index = chunk_text.find(unprocessed)
                    if unprocessed_index != -1:
                        # 从原始剩余文本中的相应位置继续
                        remaining_text = remaining_text[unprocessed_index:]
                    else:
                        # 如果找不到，尝试继续处理剩余部分
                        remaining_text = remaining_text[len(chunk_text):]
                else:
                    # 没有未处理文本，继续下一块
                    remaining_text = remaining_text[len(chunk_text):]
                
                chunk_index += 1
                
                # 防止无限循环
                if chunk_index > 100:
                    print("警告：处理块数过多，可能存在问题")
                    break
                    
            except Exception as e:
                print(f"处理文本块{chunk_index}失败: {e}")
                # 跳过当前块，继续处理
                skip_size = min(self.chunk_size // 2, len(remaining_text))
                remaining_text = remaining_text[skip_size:]
                chunk_index += 1
        
        if callback:
            callback({
                'status': 'completed',
                'message': f'解析完成，共找到{len(all_questions)}道试题',
                'progress': 100
            })
        
        # 创建试题集
        question_set = QuestionSet(
            title=Path(file_path).stem,
            questions=all_questions,
            metadata={
                'file_path': file_path,
                'file_size': os.path.getsize(file_path),
                'total_chunks': chunk_index,
                'ai_provider': self.ai_service.__class__.__name__,
                'model': self.ai_service.model_name
            }
        )
        
        # 计算总分
        total_score = sum(q.score for q in all_questions if q.score)
        if total_score > 0:
            question_set.total_score = total_score
        
        return question_set

    def _get_next_chunk(self, text: str, chunk_index: int) -> str:
        """
        动态获取下一个文本块
        
        Args:
            text: 剩余文本
            chunk_index: 当前块索引
            
        Returns:
            下一个文本块
        """
        # 基础块大小
        base_size = self.chunk_size
        
        # 如果是第一块或剩余文本较少，使用基础大小
        if chunk_index == 0 or self.ai_service.count_tokens(text) <= base_size * 1.5:
            chunk_size = base_size
        else:
            # 动态调整块大小，确保包含完整试题
            chunk_size = base_size
            
            # 查找可能的试题边界（题号模式）
            patterns = [
                r'\n\s*\d+[\.\、]\s*',  # 1. 或 1、
                r'\n\s*[一二三四五六七八九十]+[\.\、]\s*',  # 一、
                r'\n\s*[(（]\d+[)）]\s*',  # (1) 或 （1）
                r'\n\s*第\s*\d+\s*题',  # 第1题
            ]
            
            # 在块大小附近查找题目边界
            import re
            search_start = max(0, chunk_size - 200)
            search_end = min(len(text), chunk_size + 500)
            search_text = text[search_start:search_end]
            
            best_pos = chunk_size
            for pattern in patterns:
                matches = list(re.finditer(pattern, search_text))
                if matches:
                    # 找到最接近目标大小的边界
                    for match in matches:
                        pos = search_start + match.start()
                        if abs(pos - chunk_size) < abs(best_pos - chunk_size):
                            best_pos = pos
            
            chunk_size = best_pos
        
        # 确保不超过剩余文本
        chunk_size = min(chunk_size, len(text))
        
        # 获取文本块
        chunk = text[:chunk_size]
        
        # 如果块末尾在单词中间，调整到单词边界
        if chunk_size < len(text) and not text[chunk_size:chunk_size+1].isspace():
            last_space = chunk.rfind(' ')
            if last_space > chunk_size * 0.8:  # 只有在不会损失太多内容时才调整
                chunk = chunk[:last_space]
        
        return chunk 