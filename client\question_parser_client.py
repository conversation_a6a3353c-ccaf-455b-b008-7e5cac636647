"""
智能试题解析器 Python 客户端库

使用示例:
    client = QuestionParserClient("http://localhost:8000", "deepseek", "your_api_key")
    
    # 解析文本
    result = client.parse_text("你的试题文本")
"""
import requests
import time
from typing import Optional, Dict, Any, List
from pathlib import Path


class QuestionParserClient:
    """试题解析器客户端"""
    
    def __init__(self, base_url: str, ai_provider: str, api_key: str, 
                 model_name: Optional[str] = None):
        """
        初始化客户端
        
        Args:
            base_url: API服务地址，如 http://localhost:8000
            ai_provider: AI提供商 (deepseek, openai, gemini)
            api_key: API密钥
            model_name: 模型名称（可选）
        """
        self.base_url = base_url.rstrip('/')
        self.ai_provider = ai_provider
        self.api_key = api_key
        self.model_name = model_name
        self.session = requests.Session()
    

    
    def parse_text(self, text: str) -> Dict[str, Any]:
        """
        解析文本中的试题
        
        Args:
            text: 包含试题的文本
            
        Returns:
            解析结果
        """
        data = {
            'text': text,
            'ai_provider': self.ai_provider,
            'api_key': self.api_key
        }
        if self.model_name:
            data['model_name'] = self.model_name
        
        response = self.session.post(
            f"{self.base_url}/api/parse/text",
            data=data
        )
        
        if response.status_code != 200:
            raise Exception(f"解析失败: {response.text}")
        
        return response.json()
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        response = self.session.get(f"{self.base_url}/api/task/{task_id}")
        
        if response.status_code != 200:
            raise Exception(f"获取状态失败: {response.text}")
        
        return response.json()
    
    def get_results(self, task_id: str) -> Dict[str, Any]:
        """获取解析结果"""
        response = self.session.get(f"{self.base_url}/api/results/{task_id}")
        
        if response.status_code != 200:
            raise Exception(f"获取结果失败: {response.text}")
        
        return response.json()
    
    def wait_for_task(self, task_id: str, callback: Optional[callable] = None,
                      timeout: int = 300, interval: int = 2) -> Dict[str, Any]:
        """
        等待任务完成
        
        Args:
            task_id: 任务ID
            callback: 进度回调函数
            timeout: 超时时间（秒）
            interval: 轮询间隔（秒）
            
        Returns:
            解析结果
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.get_task_status(task_id)
            
            if callback:
                callback(status)
            
            if status['status'] == 'completed':
                return self.get_results(task_id)
            elif status['status'] == 'failed':
                raise Exception(f"任务失败: {status.get('error', '未知错误')}")
            
            time.sleep(interval)
        
        raise TimeoutError(f"任务超时: {timeout}秒")
    
    def get_providers(self) -> List[Dict[str, Any]]:
        """获取支持的AI提供商列表"""
        response = self.session.get(f"{self.base_url}/api/providers")
        
        if response.status_code != 200:
            raise Exception(f"获取提供商列表失败: {response.text}")
        
        return response.json()['providers']
    



# 使用示例
if __name__ == "__main__":
    # 创建客户端
    client = QuestionParserClient(
        base_url="http://localhost:8000",
        ai_provider="deepseek",
        api_key="your_api_key_here"
    )
    
    # 示例1：解析文本
    text = """
    1. Python中用于定义函数的关键字是？
    A. function
    B. def
    C. func
    D. define
    
    答案：B
    """
    
    try:
        result = client.parse_text(text)
        print(f"找到 {result['total_questions']} 道试题")
        for q in result['questions']:
            print(f"- {q['stem']}")
    except Exception as e:
        print(f"解析失败: {e}")
    
 