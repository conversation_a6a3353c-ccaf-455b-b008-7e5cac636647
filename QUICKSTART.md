# 快速开始指南

## 5分钟快速上手

### 步骤1：获取API密钥

首先，您需要获取至少一个AI服务的API密钥：

- **DeepSeek（推荐）**：
  1. 访问 https://platform.deepseek.com/
  2. 注册并获取API密钥
  3. 价格优势：输入$0.14/百万tokens，输出$0.28/百万tokens

- **OpenAI**：
  1. 访问 https://platform.openai.com/
  2. 注册并获取API密钥

- **Google Gemini**：
  1. 访问 https://makersuite.google.com/app/apikey
  2. 获取免费API密钥

### 步骤2：快速安装

```bash
# 克隆项目（如果从GitHub）
git clone <repository_url>
cd smart-question-parser

# 安装依赖
pip install -r requirements.txt

# 创建环境配置
echo "DEEPSEEK_API_KEY=your_api_key_here" > .env
```

### 步骤3：启动服务

```bash
python run.py
```

服务启动后，打开浏览器访问：http://localhost:8000

### 步骤4：使用Web界面

1. 在Web界面中：
   - 选择AI提供商（如DeepSeek）
   - 输入您的API密钥
   - 选择要解析的文件或输入文本
   - 点击"开始解析"

2. 等待解析完成，查看结果

## 测试示例

### 使用提供的示例文件

```bash
# 使用Web界面上传示例文件
examples/sample_questions.txt
```

### 使用API测试

```python
# 运行示例脚本
python example_usage.py text
```

### 使用客户端库

```python
from client.question_parser_client import QuestionParserClient

# 创建客户端
client = QuestionParserClient(
    base_url="http://localhost:8000",
    ai_provider="deepseek",
    api_key="your_api_key"
)

# 解析文本
result = client.parse_text("""
1. 什么是人工智能？
A. 一种编程语言
B. 模拟人类智能的技术
C. 一种数据库
D. 一种操作系统

答案：B
""")

print(f"找到 {result['total_questions']} 道试题")
```

## 常见使用场景

### 1. 教师整理试题

将试题文本解析为统一格式，方便管理。

### 2. 在线教育平台

集成API到您的平台：

```python
# Flask集成示例
@app.route('/parse', methods=['POST'])
def parse_questions():
    text = request.form['text']
    result = client.parse_text(text)
    # 存储到数据库
    return jsonify(result)
```

### 3. 试题格式转换

将解析后的试题转换为Moodle、Canvas等平台格式：

```python
# 转换为Moodle XML格式
questions = client.parse_text(question_text)
moodle_xml = convert_to_moodle_format(questions)
```

## 配置说明

### 基本配置

编辑 `.env` 文件：

```env
# 选择一个或多个AI服务
DEEPSEEK_API_KEY=sk-xxxxx
OPENAI_API_KEY=sk-xxxxx
GEMINI_API_KEY=xxxxx

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

# 性能配置
MAX_CONCURRENT_REQUESTS=5  # 并发请求数
CHUNK_SIZE=2000  # 每块的token数
```

### 高级配置

针对大文件优化：

```env
# 大文件处理
MAX_UPLOAD_SIZE=209715200  # 200MB
CHUNK_SIZE=3000  # 增加块大小
MAX_CONCURRENT_REQUESTS=10  # 增加并发
```

## 故障排除

### 1. "模块未找到"错误

确保在项目根目录运行：
```bash
cd /path/to/smart-question-parser
python run.py
```

### 2. API调用失败

检查：
- API密钥是否正确
- 网络连接是否正常
- API额度是否充足

### 3. 解析结果不准确

尝试：
- 使用不同的AI模型
- 调整文本分块大小
- 确保文档格式清晰

### 4. 文件上传失败

检查：
- 文件大小是否超限
- 文件格式是否支持
- 文件是否损坏

## 性能优化建议

1. **批量处理**：使用批量API减少请求次数
2. **缓存结果**：相同文件避免重复解析
3. **异步处理**：使用后台任务处理大文件
4. **负载均衡**：多个API密钥轮询使用

## 获取帮助

- 查看完整文档：[README.md](README.md)
- 部署指南：[DEPLOYMENT.md](DEPLOYMENT.md)
- API文档：http://localhost:8000/docs
- 提交问题：GitHub Issues

## 下一步

1. 探索API文档了解更多功能
2. 自定义试题格式和解析规则
3. 集成到您的应用程序
4. 优化性能和准确率

祝您使用愉快！如有问题，欢迎随时反馈。 