"""文本文件解析器"""
import chardet
from typing import Optional
from .base_parser import BaseParser


class TextParser(BaseParser):
    """文本文件（.txt）解析器"""
    
    def extract_text(self, file_path: str) -> str:
        """
        从文本文件中提取内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            提取的文本内容
        """
        # 首先检测文件编码
        encoding = self._detect_encoding(file_path)
        
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                text = f.read()
        except UnicodeDecodeError:
            # 如果检测的编码失败，尝试其他常见编码
            for enc in ['utf-8', 'gbk', 'gb2312', 'big5', 'latin-1']:
                try:
                    with open(file_path, 'r', encoding=enc) as f:
                        text = f.read()
                    break
                except UnicodeDecodeError:
                    continue
            else:
                # 如果所有编码都失败，使用错误处理模式
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    text = f.read()
        
        return text.strip()
    
    def _detect_encoding(self, file_path: str) -> Optional[str]:
        """检测文件编码"""
        with open(file_path, 'rb') as f:
            raw_data = f.read()
            result = chardet.detect(raw_data)
            return result.get('encoding', 'utf-8') 