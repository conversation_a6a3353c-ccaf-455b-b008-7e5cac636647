<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能试题解析器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div id="root"></div>
    
    <script type="text/babel">
        const { useState, useEffect, useRef } = React;
        
        const API_BASE = window.location.origin;
        
        function App() {
            const [providers, setProviders] = useState([]);
            const [selectedProvider, setSelectedProvider] = useState('deepseek');
            const [apiKey, setApiKey] = useState('');
            const [modelName, setModelName] = useState('');
            const [file, setFile] = useState(null);
            const [uploading, setUploading] = useState(false);
            const [taskId, setTaskId] = useState(null);
            const [taskStatus, setTaskStatus] = useState(null);
            const [results, setResults] = useState(null);
            const [activeTab, setActiveTab] = useState('file'); // 'file' or 'text'
            const [textInput, setTextInput] = useState('');
            const [parsingText, setParsingText] = useState(false);
            const fileInputRef = useRef(null);
            
            // 加载AI服务提供商
            useEffect(() => {
                axios.get(`${API_BASE}/api/providers`)
                    .then(res => {
                        setProviders(res.data.providers);
                        if (res.data.providers.length > 0) {
                            setModelName(res.data.providers[0].default_model);
                        }
                    })
                    .catch(err => console.error('加载提供商失败:', err));
            }, []);
            
            // 监控任务状态
            useEffect(() => {
                if (taskId && !results) {
                    const interval = setInterval(() => {
                        axios.get(`${API_BASE}/api/task/${taskId}`)
                            .then(res => {
                                setTaskStatus(res.data);
                                if (res.data.status === 'completed') {
                                    // 获取结果
                                    axios.get(`${API_BASE}/api/results/${taskId}`)
                                        .then(resultRes => {
                                            setResults(resultRes.data);
                                            clearInterval(interval);
                                        });
                                } else if (res.data.status === 'failed') {
                                    clearInterval(interval);
                                }
                            })
                            .catch(err => {
                                console.error('获取任务状态失败:', err);
                                clearInterval(interval);
                            });
                    }, 1000);
                    
                    return () => clearInterval(interval);
                }
            }, [taskId, results]);
            
            const handleFileSelect = (e) => {
                const selectedFile = e.target.files[0];
                if (selectedFile) {
                    // 验证文件大小
                    if (selectedFile.size > 104857600) {
                        alert('文件太大，最大支持100MB');
                        return;
                    }
                    setFile(selectedFile);
                    setResults(null);
                    setTaskStatus(null);
                }
            };
            
            const handleUpload = async () => {
                if (!file || !apiKey) {
                    alert('请选择文件并输入API密钥');
                    return;
                }
                
                setUploading(true);
                setTaskId(null);
                setTaskStatus(null);
                setResults(null);
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('ai_provider', selectedProvider);
                formData.append('api_key', apiKey);
                if (modelName) formData.append('model_name', modelName);
                
                try {
                    const response = await axios.post(`${API_BASE}/api/parse/file`, formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data'
                        }
                    });
                    
                    setTaskId(response.data.task_id);
                } catch (error) {
                    console.error('上传失败:', error);
                    alert('上传失败: ' + (error.response?.data?.detail || error.message));
                } finally {
                    setUploading(false);
                }
            };
            
            const handleTextParse = async () => {
                if (!textInput.trim() || !apiKey) {
                    alert('请输入文本内容和API密钥');
                    return;
                }
                
                setParsingText(true);
                setResults(null);
                
                const formData = new FormData();
                formData.append('text', textInput);
                formData.append('ai_provider', selectedProvider);
                formData.append('api_key', apiKey);
                if (modelName) formData.append('model_name', modelName);
                
                try {
                    const response = await axios.post(`${API_BASE}/api/parse/text`, formData);
                    
                    // 转换为与文件解析相同的格式
                    setResults({
                        title: "直接输入的文本",
                        questions: response.data.questions,
                        metadata: {
                            source: 'direct_text'
                        }
                    });
                } catch (error) {
                    console.error('解析失败:', error);
                    alert('解析失败: ' + (error.response?.data?.detail || error.message));
                } finally {
                    setParsingText(false);
                }
            };
            
            const reset = () => {
                setFile(null);
                setTaskId(null);
                setTaskStatus(null);
                setResults(null);
                setTextInput('');
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
            };
            
            const currentProvider = providers.find(p => p.id === selectedProvider);
            
            return (
                <div className="min-h-screen py-8 px-4">
                    <div className="max-w-6xl mx-auto">
                        <h1 className="text-4xl font-bold text-gray-800 mb-8 text-center">
                            <i className="fas fa-brain mr-3 text-blue-600"></i>
                            智能试题解析器
                        </h1>
                        
                        {/* 配置区域 */}
                        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                            <h2 className="text-xl font-semibold mb-4">AI配置</h2>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        AI服务提供商
                                    </label>
                                    <select 
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        value={selectedProvider}
                                        onChange={(e) => {
                                            setSelectedProvider(e.target.value);
                                            const provider = providers.find(p => p.id === e.target.value);
                                            if (provider) {
                                                setModelName(provider.default_model);
                                            }
                                        }}
                                    >
                                        {providers.map(provider => (
                                            <option key={provider.id} value={provider.id}>
                                                {provider.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        API密钥
                                    </label>
                                    <input
                                        type="password"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="输入API密钥"
                                        value={apiKey}
                                        onChange={(e) => setApiKey(e.target.value)}
                                    />
                                </div>
                                
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        模型
                                    </label>
                                    <select 
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        value={modelName}
                                        onChange={(e) => setModelName(e.target.value)}
                                    >
                                        {currentProvider?.models.map(model => (
                                            <option key={model} value={model}>
                                                {model}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        {/* 输入方式选择 */}
                        <div className="bg-white rounded-lg shadow-md mb-8">
                            <div className="flex border-b">
                                <button
                                    className={`flex-1 px-6 py-3 font-medium ${
                                        activeTab === 'file' 
                                            ? 'text-blue-600 border-b-2 border-blue-600' 
                                            : 'text-gray-500 hover:text-gray-700'
                                    }`}
                                    onClick={() => setActiveTab('file')}
                                >
                                    <i className="fas fa-file-upload mr-2"></i>
                                    文件上传
                                </button>
                                <button
                                    className={`flex-1 px-6 py-3 font-medium ${
                                        activeTab === 'text' 
                                            ? 'text-blue-600 border-b-2 border-blue-600' 
                                            : 'text-gray-500 hover:text-gray-700'
                                    }`}
                                    onClick={() => setActiveTab('text')}
                                >
                                    <i className="fas fa-keyboard mr-2"></i>
                                    文本输入
                                </button>
                            </div>
                            
                            <div className="p-6">
                                {activeTab === 'file' ? (
                                    <div>
                                        <div className="mb-4">
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                选择试题文件
                                            </label>
                                            <input
                                                ref={fileInputRef}
                                                type="file"
                                                accept=".txt,.pdf,.docx,.doc,.xlsx,.xls"
                                                onChange={handleFileSelect}
                                                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                                            />
                                            <p className="mt-1 text-sm text-gray-500">
                                                支持格式：txt, pdf, docx, doc, xlsx, xls（最大100MB）
                                            </p>
                                        </div>
                                        
                                        {file && (
                                            <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                                                <p className="text-sm font-medium text-blue-800">
                                                    已选择文件：{file.name}
                                                </p>
                                                <p className="text-xs text-blue-600 mt-1">
                                                    大小：{(file.size / 1024 / 1024).toFixed(2)} MB
                                                </p>
                                            </div>
                                        )}
                                        
                                        <button
                                            onClick={handleUpload}
                                            disabled={!file || !apiKey || uploading}
                                            className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
                                                file && apiKey && !uploading
                                                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                                                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            }`}
                                        >
                                            {uploading ? (
                                                <>
                                                    <i className="fas fa-spinner fa-spin mr-2"></i>
                                                    上传中...
                                                </>
                                            ) : (
                                                <>
                                                    <i className="fas fa-upload mr-2"></i>
                                                    开始解析
                                                </>
                                            )}
                                        </button>
                                    </div>
                                ) : (
                                    <div>
                                        <div className="mb-4">
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                输入试题文本
                                            </label>
                                            <textarea
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                rows={10}
                                                placeholder="请输入包含试题的文本内容..."
                                                value={textInput}
                                                onChange={(e) => setTextInput(e.target.value)}
                                            />
                                        </div>
                                        
                                        <button
                                            onClick={handleTextParse}
                                            disabled={!textInput.trim() || !apiKey || parsingText}
                                            className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
                                                textInput.trim() && apiKey && !parsingText
                                                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                                                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            }`}
                                        >
                                            {parsingText ? (
                                                <>
                                                    <i className="fas fa-spinner fa-spin mr-2"></i>
                                                    解析中...
                                                </>
                                            ) : (
                                                <>
                                                    <i className="fas fa-brain mr-2"></i>
                                                    开始解析
                                                </>
                                            )}
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>
                        
                        {/* 进度显示 */}
                        {taskStatus && !results && (
                            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                                <h2 className="text-xl font-semibold mb-4">解析进度</h2>
                                <div className="mb-4">
                                    <div className="flex justify-between mb-2">
                                        <span className="text-sm font-medium text-gray-700">
                                            {taskStatus.message}
                                        </span>
                                        <span className="text-sm font-medium text-gray-700">
                                            {Math.round(taskStatus.progress)}%
                                        </span>
                                    </div>
                                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                                        <div 
                                            className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                                            style={{ width: `${taskStatus.progress}%` }}
                                        ></div>
                                    </div>
                                </div>
                                {taskStatus.status === 'failed' && (
                                    <div className="p-4 bg-red-50 rounded-lg">
                                        <p className="text-red-800">
                                            <i className="fas fa-exclamation-circle mr-2"></i>
                                            错误：{taskStatus.error}
                                        </p>
                                    </div>
                                )}
                            </div>
                        )}
                        
                        {/* 结果显示 */}
                        {results && (
                            <div className="bg-white rounded-lg shadow-md p-6">
                                <div className="flex justify-between items-center mb-6">
                                    <h2 className="text-xl font-semibold">解析结果</h2>
                                    <button
                                        onClick={reset}
                                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                                    >
                                        <i className="fas fa-redo mr-2"></i>
                                        重新开始
                                    </button>
                                </div>
                                
                                <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div className="bg-blue-50 p-4 rounded-lg">
                                        <p className="text-sm text-blue-600 font-medium">试题总数</p>
                                        <p className="text-2xl font-bold text-blue-800">
                                            {results.questions.length}
                                        </p>
                                    </div>
                                    {results.total_score > 0 && (
                                        <div className="bg-green-50 p-4 rounded-lg">
                                            <p className="text-sm text-green-600 font-medium">总分</p>
                                            <p className="text-2xl font-bold text-green-800">
                                                {results.total_score}
                                            </p>
                                        </div>
                                    )}
                                    <div className="bg-purple-50 p-4 rounded-lg">
                                        <p className="text-sm text-purple-600 font-medium">文件名</p>
                                        <p className="text-lg font-semibold text-purple-800 truncate">
                                            {results.title}
                                        </p>
                                    </div>
                                </div>
                                
                                {/* 试题列表 */}
                                <div className="space-y-4">
                                    {results.questions.map((question, index) => (
                                        <QuestionCard key={question.id || index} question={question} index={index} />
                                    ))}
                                </div>
                                
                                {/* 导出按钮 */}
                                <div className="mt-6 flex justify-center">
                                    <button
                                        onClick={() => {
                                            const dataStr = JSON.stringify(results, null, 2);
                                            const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
                                            const exportFileDefaultName = `${results.title}_解析结果.json`;
                                            
                                            const linkElement = document.createElement('a');
                                            linkElement.setAttribute('href', dataUri);
                                            linkElement.setAttribute('download', exportFileDefaultName);
                                            linkElement.click();
                                        }}
                                        className="px-6 py-3 bg-green-600 text-white font-medium rounded-md hover:bg-green-700"
                                    >
                                        <i className="fas fa-download mr-2"></i>
                                        导出JSON
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            );
        }
        
        function QuestionCard({ question, index }) {
            const [expanded, setExpanded] = useState(false);
            
            const typeLabels = {
                single_choice: '单选题',
                multiple_choice: '多选题',
                true_false: '判断题',
                fill_in_blank: '填空题',
                short_answer: '简答题',
                essay: '论述题',
                calculation: '计算题',
                programming: '编程题'
            };
            
            const typeColors = {
                single_choice: 'blue',
                multiple_choice: 'indigo',
                true_false: 'green',
                fill_in_blank: 'yellow',
                short_answer: 'purple',
                essay: 'pink',
                calculation: 'orange',
                programming: 'gray'
            };
            
            const color = typeColors[question.type] || 'gray';
            
            return (
                <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div 
                        className="cursor-pointer"
                        onClick={() => setExpanded(!expanded)}
                    >
                        <div className="flex items-start justify-between">
                            <div className="flex-1">
                                <div className="flex items-center gap-3 mb-2">
                                    <span className="text-lg font-semibold text-gray-700">
                                        #{index + 1}
                                    </span>
                                    <span className={`px-2 py-1 text-xs font-medium rounded bg-${color}-100 text-${color}-800`}>
                                        {typeLabels[question.type] || question.type}
                                    </span>
                                    {question.score && (
                                        <span className="px-2 py-1 text-xs font-medium rounded bg-gray-100 text-gray-800">
                                            {question.score}分
                                        </span>
                                    )}
                                </div>
                                <p className="text-gray-800 font-medium">
                                    {question.stem}
                                </p>
                            </div>
                            <i className={`fas fa-chevron-${expanded ? 'up' : 'down'} text-gray-400 ml-4`}></i>
                        </div>
                    </div>
                    
                    {expanded && (
                        <div className="mt-4 space-y-3">
                            {/* 选项 */}
                            {question.choices && question.choices.length > 0 && (
                                <div>
                                    <p className="text-sm font-medium text-gray-600 mb-2">选项：</p>
                                    <div className="space-y-1">
                                        {question.choices.map((choice, i) => (
                                            <div key={i} className="flex items-start">
                                                <span className="font-medium text-gray-700 mr-2">
                                                    {choice.label}.
                                                </span>
                                                <span className="text-gray-600">{choice.content}</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                            
                            {/* 答案 */}
                            {question.answer && (
                                <div>
                                    <p className="text-sm font-medium text-gray-600">答案：</p>
                                    <p className="text-green-600 font-medium">{question.answer}</p>
                                </div>
                            )}
                            
                            {/* 解析 */}
                            {question.analysis && (
                                <div>
                                    <p className="text-sm font-medium text-gray-600">解析：</p>
                                    <p className="text-gray-700">{question.analysis}</p>
                                </div>
                            )}
                            
                            {/* 元数据 */}
                            <div className="flex flex-wrap gap-2 mt-3">
                                {question.subject && (
                                    <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">
                                        科目：{question.subject}
                                    </span>
                                )}
                                {question.chapter && (
                                    <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">
                                        章节：{question.chapter}
                                    </span>
                                )}
                                {question.difficulty && (
                                    <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">
                                        难度：{question.difficulty}
                                    </span>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            );
        }
        
        ReactDOM.createRoot(document.getElementById('root')).render(<App />);
    </script>
</body>
</html> 