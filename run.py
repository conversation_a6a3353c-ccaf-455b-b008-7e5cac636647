"""启动脚本"""
import os
import sys
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

if __name__ == "__main__":
    import uvicorn
    from dotenv import load_dotenv
    
    # 加载环境变量
    load_dotenv()
    
    # 获取配置
    host = os.getenv("SERVER_HOST", "0.0.0.0")
    port = int(os.getenv("SERVER_PORT", 8000))
    
    print(f"启动服务器在 http://{host}:{port}")
    print(f"API文档: http://{host}:{port}/docs")
    print(f"Web界面: http://{host}:{port}/")
    
    # 启动服务器
    uvicorn.run(
        "src.api.main:app",
        host=host,
        port=port,
        reload=True
    ) 