# 智能试题解析器

一个使用AI大模型（DeepSeek、Gemini、OpenAI、OpenRouter等）自动解析各种文档格式中试题的应用。支持将不同格式的试题转换为统一的结构化格式。

## 功能特点

- **多格式支持**：支持 TXT、PDF、Word（.docx）、Excel（.xlsx/.xls）等多种文档格式
- **智能解析**：使用先进的AI模型自动识别和提取试题
- **大文件处理**：支持最大100MB的文件，自动分块处理以适应模型token限制
- **统一格式**：将各种格式的试题转换为统一的JSON格式
- **并发处理**：支持并发请求，提高处理效率
- **Web界面**：提供友好的Web界面进行测试
- **RESTful API**：提供标准的API接口，方便集成

## 支持的试题类型

- 单选题
- 多选题
- 判断题
- 填空题
- 简答题
- 论述题
- 计算题
- 编程题

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

创建 `.env` 文件并配置以下内容：

```env
# AI API Keys (至少配置一个)
OPENAI_API_KEY=your_openai_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key  
GEMINI_API_KEY=your_gemini_api_key
OPENROUTER_API_KEY=your_openrouter_api_key

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
```

### 3. 启动服务

```bash
python run.py
```

服务启动后，访问：
- Web界面：http://localhost:8000
- API文档：http://localhost:8000/docs

## API使用示例

### 上传文件解析

```python
import requests

# 上传文件
with open('questions.pdf', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/api/parse/file',
        files={'file': f},
        data={
            'ai_provider': 'deepseek',
            'api_key': 'your_api_key'
        }
    )

task_id = response.json()['task_id']

# 查询任务状态
status = requests.get(f'http://localhost:8000/api/task/{task_id}')
print(status.json())

# 获取结果
results = requests.get(f'http://localhost:8000/api/results/{task_id}')
print(results.json())
```

### 直接解析文本

```python
response = requests.post(
    'http://localhost:8000/api/parse/text',
    data={
        'text': '你的试题文本内容',
        'ai_provider': 'deepseek',
        'api_key': 'your_api_key'
    }
)
print(response.json())
```

## OpenRouter 集成

OpenRouter 是一个统一的AI API网关，支持访问多个不同的AI模型，包括OpenAI、Anthropic、Meta、Google等。

### 支持的模型

通过OpenRouter，您可以访问以下模型：

- **OpenAI**: gpt-3.5-turbo, gpt-4, gpt-4-turbo
- **Anthropic**: claude-3-haiku, claude-3-sonnet, claude-3-opus  
- **Meta**: llama-2-70b-chat, codellama-34b-instruct
- **Google**: gemini-pro, gemini-pro-vision, gemini-2.5-flash-lite-preview-06-17, gemini-2.5-pro, gemini-2.5-flash
- **Mistral**: mistral-7b-instruct, mixtral-8x7b-instruct
- **Cohere**: command-r, command-r-plus

### OpenRouter 配置

1. 访问 [OpenRouter](https://openrouter.ai/) 注册账号
2. 获取API密钥
3. 配置环境变量：

```env
OPENROUTER_API_KEY=your_openrouter_api_key
```

### 使用示例

```python
# 使用OpenRouter解析试题
response = requests.post(
    'http://localhost:8000/api/parse/text',
    data={
        'text': '你的试题文本内容',
        'ai_provider': 'openrouter',
        'api_key': 'your_openrouter_api_key',
        'model_name': 'anthropic/claude-3-haiku'  # 可选，指定具体模型
    }
)
```

### 运行OpenRouter示例

```bash
# 设置环境变量
export OPENROUTER_API_KEY=your_openrouter_api_key

# 运行示例
python example_openrouter_usage.py
```

## 输出格式示例

```json
{
  "title": "数学测试",
  "questions": [
    {
      "id": "uuid",
      "type": "single_choice",
      "stem": "1 + 1 等于多少？",
      "choices": [
        {"label": "A", "content": "1"},
        {"label": "B", "content": "2"},
        {"label": "C", "content": "3"},
        {"label": "D", "content": "4"}
      ],
      "answer": "B",
      "analysis": "1加1等于2",
      "score": 5,
      "difficulty": "easy"
    }
  ],
  "total_score": 100
}
```

## 项目结构

```
smart-question-parser/
├── src/
│   ├── api/           # API接口
│   ├── models/        # 数据模型
│   ├── parsers/       # 文档解析器
│   └── services/      # 核心服务
├── static/            # Web前端
├── uploads/           # 临时文件目录
├── results/           # 结果存储目录
├── requirements.txt   # 依赖列表
├── run.py            # 启动脚本
└── README.md         # 说明文档
```

## 注意事项

1. **API密钥安全**：请妥善保管API密钥，不要提交到版本控制系统
2. **文件大小限制**：默认最大支持100MB的文件
3. **Token限制**：自动分块处理以适应各个模型的token限制
4. **并发限制**：默认最大并发请求数为5，可根据需要调整

## 开发计划

- [ ] 支持更多文档格式（Markdown、LaTeX等）
- [ ] 添加数据库存储
- [ ] 支持批量文件处理
- [ ] 添加用户认证
- [ ] 优化解析准确率
- [ ] 支持自定义试题模板

## 许可证

MIT License 