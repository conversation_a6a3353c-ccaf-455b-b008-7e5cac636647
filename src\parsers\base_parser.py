"""基础文档解析器"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
import hashlib
import tiktoken
from pathlib import Path


class BaseParser(ABC):
    """文档解析器基类"""
    
    def __init__(self, chunk_size: int = 2000, overlap: int = 200):
        """
        初始化解析器
        
        Args:
            chunk_size: 每个块的最大token数
            overlap: 块之间的重叠token数
        """
        self.chunk_size = chunk_size
        self.overlap = overlap
        self.encoding = tiktoken.get_encoding("cl100k_base")
    
    @abstractmethod
    def extract_text(self, file_path: str) -> str:
        """
        从文件中提取文本内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            提取的文本内容
        """
        pass
    
    def count_tokens(self, text: str) -> int:
        """计算文本的token数量"""
        return len(self.encoding.encode(text))
    
    def split_text_into_chunks(self, text: str) -> List[Dict[str, Any]]:
        """
        将文本分割成块，确保每块不超过token限制
        
        Args:
            text: 要分割的文本
            
        Returns:
            文本块列表，每个块包含内容和元数据
        """
        # 按段落分割
        paragraphs = text.split('\n\n')
        chunks = []
        current_chunk = []
        current_tokens = 0
        
        for paragraph in paragraphs:
            paragraph_tokens = self.count_tokens(paragraph)
            
            # 如果单个段落超过chunk_size，需要进一步分割
            if paragraph_tokens > self.chunk_size:
                # 保存当前块
                if current_chunk:
                    chunks.append({
                        'content': '\n\n'.join(current_chunk),
                        'tokens': current_tokens,
                        'index': len(chunks)
                    })
                    current_chunk = []
                    current_tokens = 0
                
                # 分割长段落
                sentences = self._split_into_sentences(paragraph)
                for sentence in sentences:
                    sentence_tokens = self.count_tokens(sentence)
                    if current_tokens + sentence_tokens > self.chunk_size:
                        if current_chunk:
                            chunks.append({
                                'content': ' '.join(current_chunk),
                                'tokens': current_tokens,
                                'index': len(chunks)
                            })
                            # 添加重叠内容
                            if self.overlap > 0 and current_chunk:
                                overlap_text = ' '.join(current_chunk[-2:])
                                current_chunk = [overlap_text, sentence]
                                current_tokens = self.count_tokens(' '.join(current_chunk))
                            else:
                                current_chunk = [sentence]
                                current_tokens = sentence_tokens
                    else:
                        current_chunk.append(sentence)
                        current_tokens += sentence_tokens
            
            # 正常大小的段落
            elif current_tokens + paragraph_tokens > self.chunk_size:
                # 保存当前块
                chunks.append({
                    'content': '\n\n'.join(current_chunk),
                    'tokens': current_tokens,
                    'index': len(chunks)
                })
                # 添加重叠内容
                if self.overlap > 0 and current_chunk:
                    overlap_text = current_chunk[-1]
                    current_chunk = [overlap_text, paragraph]
                    current_tokens = self.count_tokens('\n\n'.join(current_chunk))
                else:
                    current_chunk = [paragraph]
                    current_tokens = paragraph_tokens
            else:
                current_chunk.append(paragraph)
                current_tokens += paragraph_tokens
        
        # 保存最后一个块
        if current_chunk:
            chunks.append({
                'content': '\n\n'.join(current_chunk),
                'tokens': current_tokens,
                'index': len(chunks)
            })
        
        return chunks
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """将文本分割成句子"""
        # 简单的句子分割，可以根据需要改进
        import re
        sentences = re.split(r'(?<=[。！？.!?])\s+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def parse(self, file_path: str) -> Tuple[str, List[Dict[str, Any]]]:
        """
        解析文档
        
        Args:
            file_path: 文件路径
            
        Returns:
            (完整文本, 文本块列表)
        """
        text = self.extract_text(file_path)
        chunks = self.split_text_into_chunks(text)
        return text, chunks
    
    def get_file_hash(self, file_path: str) -> str:
        """计算文件的哈希值"""
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """获取文件信息"""
        path = Path(file_path)
        return {
            'name': path.name,
            'size': path.stat().st_size,
            'extension': path.suffix,
            'hash': self.get_file_hash(file_path)
        } 