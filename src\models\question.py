"""试题数据模型"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum
from datetime import datetime


class QuestionType(str, Enum):
    """试题类型枚举"""
    SINGLE_CHOICE = "single_choice"  # 单选题
    MULTIPLE_CHOICE = "multiple_choice"  # 多选题
    TRUE_FALSE = "true_false"  # 判断题
    FILL_IN_BLANK = "fill_in_blank"  # 填空题
    SHORT_ANSWER = "short_answer"  # 简答题
    ESSAY = "essay"  # 论述题
    CALCULATION = "calculation"  # 计算题
    PROGRAMMING = "programming"  # 编程题


class DifficultyLevel(str, Enum):
    """难度等级"""
    EASY = "easy"
    MEDIUM = "medium"
    HARD = "hard"
    EXPERT = "expert"


class Choice(BaseModel):
    """选择题选项"""
    label: str = Field(..., description="选项标签（如A、B、C、D）")
    content: str = Field(..., description="选项内容")


class Question(BaseModel):
    """统一的试题格式"""
    id: Optional[str] = Field(None, description="试题唯一ID")
    type: QuestionType = Field(..., description="试题类型")
    subject: Optional[str] = Field(None, description="科目")
    chapter: Optional[str] = Field(None, description="章节")
    difficulty: Optional[DifficultyLevel] = Field(None, description="难度等级")
    score: Optional[float] = Field(None, description="分值")
    
    # 题目内容
    stem: str = Field(..., description="题干内容")
    choices: Optional[List[Choice]] = Field(None, description="选择题选项")
    
    # 答案和解析
    answer: Optional[str] = Field(None, description="答案")
    analysis: Optional[str] = Field(None, description="解析")
    
    # 元数据
    tags: Optional[List[str]] = Field(default_factory=list, description="标签")
    source: Optional[str] = Field(None, description="来源")
    year: Optional[int] = Field(None, description="年份")
    created_at: Optional[datetime] = Field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="其他元数据")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class QuestionSet(BaseModel):
    """试题集合"""
    title: Optional[str] = Field(None, description="试题集标题")
    description: Optional[str] = Field(None, description="试题集描述")
    questions: List[Question] = Field(default_factory=list, description="试题列表")
    total_score: Optional[float] = Field(None, description="总分")
    time_limit: Optional[int] = Field(None, description="时限（分钟）")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        } 