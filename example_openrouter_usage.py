#!/usr/bin/env python3
"""
OpenRouter集成示例

演示如何使用OpenRouter服务来解析试题
"""

import asyncio
import os
from dotenv import load_dotenv
from src.services.ai_service import OpenRouterService

# 加载环境变量
load_dotenv()

async def main():
    """主函数"""
    
    # 从环境变量获取API密钥
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("请设置OPENROUTER_API_KEY环境变量")
        return
    
    # 支持的模型示例
    models = [
        "openai/gpt-3.5-turbo",                           # OpenAI GPT-3.5
        "openai/gpt-4",                                   # OpenAI GPT-4
        "anthropic/claude-3-haiku",                       # Anthropic Claude 3 Haiku
        "anthropic/claude-3-sonnet",                      # Anthropic Claude 3 Sonnet
        "meta-llama/llama-2-70b-chat",                    # Meta Llama 2 70B
        "google/gemini-pro",                              # Google Gemini Pro
        "google/gemini-2.5-flash-lite-preview-06-17",    # Google Gemini 2.5 Flash Lite Preview
        "google/gemini-2.5-pro",                          # Google Gemini 2.5 Pro
        "google/gemini-2.5-flash",                        # Google Gemini 2.5 Flash
        "mistralai/mistral-7b-instruct",                  # Mistral 7B
        "cohere/command-r-plus",                          # Cohere Command R+
    ]
    
    print("OpenRouter智能试题解析器示例")
    print("=" * 50)
    
    # 选择模型
    print("\n支持的模型:")
    for i, model in enumerate(models, 1):
        print(f"{i}. {model}")
    
    try:
        choice = int(input(f"\n请选择模型 (1-{len(models)}): ")) - 1
        if choice < 0 or choice >= len(models):
            print("无效选择，使用默认模型")
            choice = 0
        
        selected_model = models[choice]
        print(f"已选择模型: {selected_model}")
        
    except ValueError:
        selected_model = models[0]
        print(f"使用默认模型: {selected_model}")
    
    # 创建OpenRouter服务
    service = OpenRouterService(
        api_key=api_key,
        model_name=selected_model,
        max_tokens=4000
    )
    
    # 示例试题文本
    sample_questions = """
    1. 以下哪个是Python的数据类型？
    A. int
    B. string
    C. boolean
    D. 以上都是
    
    2. Python中用于定义函数的关键字是什么？
    A. def
    B. function
    C. define
    D. func
    
    3. 判断题：Python是一种解释型语言。
    
    4. 填空题：在Python中，_____ 用于创建列表。
    
    5. 简答题：请简述Python的主要特点。
    """
    
    print(f"\n解析示例试题...")
    print("=" * 30)
    print(sample_questions)
    print("=" * 30)
    
    try:
        # 解析试题
        questions, remaining_text = await service.parse_questions_with_remaining(sample_questions)
        
        print(f"\n✅ 成功解析 {len(questions)} 道试题")
        
        # 显示解析结果
        for i, question in enumerate(questions, 1):
            print(f"\n--- 试题 {i} ---")
            print(f"类型: {question.type.value}")
            print(f"题干: {question.stem}")
            
            if question.choices:
                print("选项:")
                for choice in question.choices:
                    print(f"  {choice.label}. {choice.content}")
            
            if question.answer:
                print(f"答案: {question.answer}")
            
            if question.analysis:
                print(f"解析: {question.analysis}")
            
            if question.score:
                print(f"分值: {question.score}")
        
        if remaining_text:
            print(f"\n⚠️ 剩余未解析文本:")
            print(remaining_text)
    
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return
    
    print(f"\n🎉 OpenRouter集成测试完成！")
    print(f"使用模型: {selected_model}")


if __name__ == "__main__":
    asyncio.run(main()) 