# 部署指南

## 开发环境部署

### 1. 环境准备

确保已安装：
- Python 3.8+
- pip

### 2. 安装依赖

```bash
# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置环境变量

创建 `.env` 文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，添加至少一个AI服务的API密钥：

```env
# DeepSeek（推荐，性价比高）
DEEPSEEK_API_KEY=your_deepseek_api_key

# 或 OpenAI
OPENAI_API_KEY=your_openai_api_key

# 或 Google Gemini
GEMINI_API_KEY=your_gemini_api_key
```

### 4. 启动服务

```bash
python run.py
```

访问：
- Web界面：http://localhost:8000
- API文档：http://localhost:8000/docs

## 生产环境部署

### 使用 Docker

创建 `Dockerfile`：

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "src.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

构建和运行：

```bash
# 构建镜像
docker build -t question-parser .

# 运行容器
docker run -d -p 8000:8000 --env-file .env question-parser
```

### 使用 Docker Compose

创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    env_file:
      - .env
    volumes:
      - ./uploads:/app/uploads
      - ./results:/app/results
    restart: unless-stopped
```

启动：

```bash
docker-compose up -d
```

### 使用 systemd（Linux）

创建服务文件 `/etc/systemd/system/question-parser.service`：

```ini
[Unit]
Description=Smart Question Parser
After=network.target

[Service]
Type=exec
User=www-data
WorkingDirectory=/path/to/smart-question-parser
Environment="PATH=/path/to/venv/bin"
ExecStart=/path/to/venv/bin/uvicorn src.api.main:app --host 0.0.0.0 --port 8000
Restart=on-failure

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl enable question-parser
sudo systemctl start question-parser
```

### 使用 Nginx 反向代理

Nginx 配置示例：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 性能优化

### 1. 使用生产级ASGI服务器

```bash
# 使用 gunicorn
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker src.api.main:app
```

### 2. 配置并发限制

在 `.env` 中调整：

```env
# 根据API配额调整
MAX_CONCURRENT_REQUESTS=10
CHUNK_SIZE=3000
```

### 3. 添加缓存

可以添加Redis缓存相同文档的解析结果：

```python
# 安装 redis
pip install redis

# 在代码中集成缓存逻辑
```

## 监控和日志

### 1. 日志配置

创建 `logging.conf`：

```ini
[loggers]
keys=root

[handlers]
keys=fileHandler,consoleHandler

[formatters]
keys=simpleFormatter

[logger_root]
level=INFO
handlers=fileHandler,consoleHandler

[handler_fileHandler]
class=FileHandler
level=INFO
formatter=simpleFormatter
args=('app.log', 'a')

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=simpleFormatter
args=(sys.stdout,)

[formatter_simpleFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s
```

### 2. 健康检查端点

API已包含健康检查：

```bash
curl http://localhost:8000/
```

## 安全建议

1. **API密钥管理**
   - 不要在代码中硬编码API密钥
   - 使用环境变量或密钥管理服务
   - 定期轮换密钥

2. **文件上传安全**
   - 限制文件大小（已实现100MB限制）
   - 验证文件类型（已实现）
   - 定期清理上传目录

3. **访问控制**
   - 在生产环境添加身份验证
   - 使用HTTPS
   - 配置CORS策略

4. **速率限制**
   - 使用Nginx或API网关实现速率限制
   - 防止API滥用

## 备份策略

定期备份：
- 解析结果（`results/`目录）
- 应用日志
- 环境配置

```bash
# 备份脚本示例
#!/bin/bash
BACKUP_DIR="/backup/question-parser"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/results_$DATE.tar.gz results/
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz logs/
```

## 故障排除

### 常见问题

1. **模块导入错误**
   ```bash
   # 确保在项目根目录运行
   cd /path/to/smart-question-parser
   python run.py
   ```

2. **API调用失败**
   - 检查API密钥是否正确
   - 检查网络连接
   - 查看日志文件

3. **文件解析失败**
   - 确保文件格式正确
   - 检查文件编码
   - 查看具体错误信息

### 日志位置

- 应用日志：`app.log`
- 上传文件：`uploads/`
- 解析结果：`results/`

## 联系支持

如有问题，请查看：
- API文档：`/docs`
- 项目README
- GitHub Issues 