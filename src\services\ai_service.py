"""AI服务模块"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
import os
from tenacity import retry, stop_after_attempt, wait_exponential
import openai
import google.generativeai as genai
import httpx
import json
from ..models.question import Question, QuestionSet, QuestionType, Choice


class AIService(ABC):
    """AI服务基类"""
    
    def __init__(self, api_key: str, model_name: str, max_tokens: int = 4000):
        self.api_key = api_key
        self.model_name = model_name
        self.max_tokens = max_tokens
    
    @abstractmethod
    async def parse_questions(self, text: str, context: Optional[str] = None) -> List[Question]:
        """
        解析文本中的试题
        
        Args:
            text: 包含试题的文本
            context: 额外的上下文信息
            
        Returns:
            解析后的试题列表
        """
        pass
    
    @abstractmethod
    async def parse_questions_with_remaining(self, text: str, 
                                            context: Optional[str] = None) -> Tuple[List[Question], str]:
        """
        解析文本中的试题，返回已解析试题和剩余文本
        
        Args:
            text: 包含试题的文本
            context: 额外的上下文信息
            
        Returns:
            (解析后的试题列表, 未解析的剩余文本)
        """
        pass
    
    def count_tokens(self, text: str) -> int:
        """
        估算文本的token数量
        
        Args:
            text: 文本内容
            
        Returns:
            估算的token数量
        """
        # 简单的估算方法：中文字符算2个token，英文单词算1个token
        # 这是一个粗略的估算，实际token数可能有所不同
        import re
        
        # 统计中文字符
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        
        # 统计英文单词（包括数字）
        english_words = len(re.findall(r'\b[a-zA-Z0-9]+\b', text))
        
        # 统计标点符号和其他字符
        other_chars = len(re.findall(r'[^\u4e00-\u9fff\w\s]', text))
        
        # 估算总token数
        # 中文字符约2个token，英文单词约1个token，标点符号约1个token
        estimated_tokens = chinese_chars * 2 + english_words + other_chars
        
        return estimated_tokens
    
    def create_prompt(self, text: str, context: Optional[str] = None) -> str:
        """创建提示词"""
        prompt = """你是一个专业的试题解析助手。请尽可能多地解析文本中的试题，不要轻易放弃。

核心原则：积极解析，最大化提取试题数量！

支持的试题格式：
1. 标准格式：
   - 单选：1. 题干 A.选项1 B.选项2 答案：A
   - 多选：2. 题干 A.选项1 B.选项2 答案：AB 或 A、B
   - 判断：3. 题干 答案：正确/错误/√/×
   - 填空：4. 题干______ 答案：填入内容

2. 特殊符号格式：
   - ◯ A. 选项1  ◉ B. 选项2 ✅  (◉或✅表示正确)
   - □ A. 选项1  ☑ B. 选项2 ✅  (☑或✅表示正确，多个表示多选)
   - ( ) 判断题题干  ( × ) 解析 (×表示错误，√表示正确)
   - 题干 ✅ 直接在题目后标记

3. 答案识别规则：
   - ✅、◉、☑ = 该选项正确
   - ( × ) = 错误，( √ ) = 正确
   - "答案：A" 或直接在选项后标记
   - 多个✅或☑ = 多选题

关键规则：
1. 只要能找到题干和答案，就必须解析！即使格式不标准！
2. 不要因为格式问题就放到remaining_text中
3. 答案可能用各种方式标记，要灵活识别
4. 重复的试题也要分别解析
5. remaining_text只用于：
   - 题干明显被截断（如句子不完整）
   - 完全无法理解的乱码内容
   - 纯粹的说明文字（非试题内容）

输出格式：
{
  "parsed_questions": [
    {
      "type": "single_choice|multiple_choice|true_false|fill_in_blank|short_answer",
      "stem": "题干内容",
      "choices": [{"label": "A", "content": "选项内容"}],
      "answer": "答案",
      "analysis": "解析内容",
      "score": 分值
    }
  ],
  "remaining_text": ""
}

类型识别：
- single_choice: 只有一个正确答案
- multiple_choice: 多个正确答案（有多个✅、☑或字母组合）
- true_false: 对错判断（√/×、正确/错误、对/错、T/F）
- fill_in_blank: 有______空白或（填空）标记
- short_answer: 需要文字回答

答案转换：
- ✅、◉、☑符号 → 对应选项字母（A、B、C、D）
- ( × ) → "错误"，( √ ) → "正确"
- 多个标记 → 组合字母（如A、B、C）

示例解析：
输入："下列项目中属于流动负债的是：A. 应付债券 B. 预付账款 C. 一年内到期的长期借款 ✅ D. 长期应收款"
输出：{"type": "single_choice", "stem": "下列项目中属于流动负债的是：", "choices": [{"label": "A", "content": "应付债券"}, {"label": "B", "content": "预付账款"}, {"label": "C", "content": "一年内到期的长期借款"}, {"label": "D", "content": "长期应收款"}], "answer": "C"}

记住：宁可多解析也不要遗漏！如果不确定是否为试题，倾向于解析！
"""
        if context:
            prompt += f"\n额外信息：{context}\n"
        
        prompt += f"\n请解析以下文本：\n{text}"
        
        return prompt
    
    def parse_response(self, response: str) -> Tuple[List[Question], str]:
        """解析AI响应，返回(试题列表, 剩余文本)"""
        try:
            # 提取JSON部分
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                response = response[json_start:json_end]
            elif "```" in response:
                json_start = response.find("```") + 3
                json_end = response.find("```", json_start)
                response = response[json_start:json_end]
            
            # 解析JSON
            data = json.loads(response.strip())
            
            # 获取已解析的试题
            questions_data = data.get('parsed_questions', [])
            remaining_text = data.get('remaining_text', '')
            
            questions = []
            for q_data in questions_data:
                try:
                    # 转换试题类型
                    type_mapping = {
                        # 中文类型
                        '单选题': QuestionType.SINGLE_CHOICE,
                        '多选题': QuestionType.MULTIPLE_CHOICE,
                        '判断题': QuestionType.TRUE_FALSE,
                        '填空题': QuestionType.FILL_IN_BLANK,
                        '简答题': QuestionType.SHORT_ANSWER,
                        # 英文类型
                        'single_choice': QuestionType.SINGLE_CHOICE,
                        'multiple_choice': QuestionType.MULTIPLE_CHOICE,
                        'true_false': QuestionType.TRUE_FALSE,
                        'fill_in_blank': QuestionType.FILL_IN_BLANK,
                        'short_answer': QuestionType.SHORT_ANSWER,
                    }
                    
                    question_type = q_data.get('type', '')
                    if question_type in type_mapping:
                        question_type = type_mapping[question_type]
                    elif question_type not in QuestionType.__members__.values():
                        # 根据答案和选项智能推断类型
                        answer = q_data.get('answer', '')
                        choices = q_data.get('choices', [])
                        
                        if self._is_true_false_answer(answer):
                            question_type = QuestionType.TRUE_FALSE
                        elif choices and self._is_multiple_choice_answer(answer):
                            question_type = QuestionType.MULTIPLE_CHOICE
                        elif choices:
                            question_type = QuestionType.SINGLE_CHOICE
                        else:
                            question_type = QuestionType.SHORT_ANSWER
                    
                    # 处理选项
                    choices = None
                    if 'choices' in q_data and q_data['choices']:
                        choices = []
                        for choice in q_data['choices']:
                            if isinstance(choice, dict):
                                # 确保content不包含标签
                                content = choice.get('content', '')
                                label = choice.get('label', chr(65 + len(choices)))
                                # 如果content以标签开头，去除标签部分
                                content = self._clean_choice_content(content, label)
                                choices.append(Choice(label=label, content=content))
                            else:
                                # 简单格式转换
                                label = chr(65 + len(choices))  # A, B, C, ...
                                content = str(choice)
                                # 清理可能包含的标签
                                content = self._clean_choice_content(content, label)
                                choices.append(Choice(label=label, content=content))
                    
                    # 处理答案，特别是判断题答案标准化
                    answer = q_data.get('answer')
                    if question_type == QuestionType.TRUE_FALSE and answer:
                        answer = self._normalize_true_false_answer(answer)
                    
                    # 创建Question对象
                    question = Question(
                        type=question_type,
                        stem=q_data.get('stem', ''),
                        choices=choices,
                        answer=answer,
                        analysis=q_data.get('analysis'),
                        score=q_data.get('score'),
                        subject=q_data.get('subject'),
                        chapter=q_data.get('chapter'),
                        difficulty=q_data.get('difficulty'),
                        tags=q_data.get('tags', []),
                        source=q_data.get('source'),
                        year=q_data.get('year')
                    )
                    
                    questions.append(question)
                
                except Exception as e:
                    print(f"解析单个试题失败: {e}")
                    continue
            
            return questions, remaining_text
        
        except Exception as e:
            print(f"解析AI响应失败: {e}，response: {response}")
            return [], ""
    
    def _is_true_false_answer(self, answer: str) -> bool:
        """判断是否为判断题答案"""
        if not answer:
            return False
        answer_lower = answer.lower().strip()
        true_false_answers = [
            '正确', '错误', '对', '错', '√', '×', 
            'true', 'false', 't', 'f', '是', '否',
            '正确答案', '错误答案', '对的', '错的',
            '正', '真', '成立', '✓', 'yes', 'y', '是的',
            '不对', '不正确', '假', '不成立', '✗', 'no', 'n', '不是'
        ]
        return answer_lower in true_false_answers
    
    def _is_multiple_choice_answer(self, answer: str) -> bool:
        """判断是否为多选题答案"""
        if not answer:
            return False
        # 检查是否包含多个选项字母
        answer = answer.upper().replace(' ', '').replace(',', '').replace('、', '')
        # 匹配多个字母模式，如ABC、A B C、A,B,C等
        import re
        pattern = r'[A-Z]{2,}|[A-Z]\s*[,、]\s*[A-Z]'
        return bool(re.search(pattern, answer))
    
    def _clean_choice_content(self, content: str, label: str) -> str:
        """清理选项内容，去除可能重复的标签和特殊符号"""
        if not content:
            return content
        
        content = content.strip()
        # 去除开头的标签模式和特殊符号
        import re
        patterns = [
            rf'^[◯◉□☑]\s*{label}[.、）)]?\s*',  # 匹配符号+标签，如 "◯ A. "
            rf'^{label}[.、）)]?\s*',  # 匹配标签开头，如 "A. "
            rf'^[A-Z][.、）)]?\s*',    # 匹配任意字母标签开头
            rf'^[◯◉□☑]\s*',         # 匹配单独的符号
            rf'\s*✅$',               # 去除末尾的✅符号
        ]
        
        for pattern in patterns:
            content = re.sub(pattern, '', content)
        
        return content.strip()
    
    def _normalize_true_false_answer(self, answer: str) -> str:
        """标准化判断题答案为'正确'或'错误'"""
        if not answer:
            return answer
        
        answer_lower = answer.lower().strip()
        
        # 正确答案的各种表达方式
        correct_answers = [
            '正确', '对', '√', 'true', 't', '是', '正确答案', '对的', 
            '正', '对的', '真', '成立', '✓', 'yes', 'y', '是的'
        ]
        
        # 错误答案的各种表达方式
        wrong_answers = [
            '错误', '错', '×', 'false', 'f', '否', '错误答案', '错的',
            '不对', '不正确', '假', '不成立', '✗', 'no', 'n', '不是'
        ]
        
        if answer_lower in correct_answers:
            return '正确'
        elif answer_lower in wrong_answers:
            return '错误'
        else:
            # 如果无法识别，保持原样
            return answer


class OpenAIService(AIService):
    """OpenAI服务（包括DeepSeek）"""
    
    def __init__(self, api_key: str, model_name: str = "gpt-3.5-turbo", 
                 base_url: Optional[str] = None, max_tokens: int = 4000):
        super().__init__(api_key, model_name, max_tokens)
        self.client = openai.AsyncOpenAI(
            api_key=api_key,
            base_url=base_url
        )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def parse_questions_with_remaining(self, text: str, 
                                           context: Optional[str] = None) -> Tuple[List[Question], str]:
        """使用OpenAI API解析试题，返回剩余文本"""
        prompt = self.create_prompt(text, context)
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "你是一个专业的试题解析助手。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=0.3
            )
            
            content = response.choices[0].message.content
            return self.parse_response(content)
        
        except Exception as e:
            print(f"OpenAI API调用失败: {e}")
            raise
    
    # 保留原有方法以兼容
    async def parse_questions(self, text: str, context: Optional[str] = None) -> List[Question]:
        """使用OpenAI API解析试题（兼容旧接口）"""
        questions, _ = await self.parse_questions_with_remaining(text, context)
        return questions


class DeepSeekService(OpenAIService):
    """DeepSeek服务"""
    
    def __init__(self, api_key: str, model_name: str = "deepseek-chat", max_tokens: int = 4000):
        super().__init__(
            api_key=api_key,
            model_name=model_name,
            base_url="https://api.deepseek.com/v1",
            max_tokens=max_tokens
        )


class OpenRouterService(OpenAIService):
    """OpenRouter服务"""
    
    def __init__(self, api_key: str, model_name: str = "openai/gpt-3.5-turbo", max_tokens: int = 4000):
        # 不使用父类的__init__，直接创建client以支持自定义配置
        AIService.__init__(self, api_key, model_name, max_tokens)
        # 创建支持UTF-8编码的客户端
        self.client = openai.AsyncOpenAI(
            api_key=api_key,
            base_url="https://openrouter.ai/api/v1",
            http_client=httpx.AsyncClient(
                headers={"Content-Type": "application/json; charset=utf-8"}
            )
        )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def parse_questions_with_remaining(self, text: str, 
                                           context: Optional[str] = None) -> Tuple[List[Question], str]:
        """使用OpenRouter API解析试题，返回剩余文本"""
        prompt = self.create_prompt(text, context)
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "你是一个专业的试题解析助手。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=0.3,
                # OpenRouter特有的headers
                extra_headers={
                    "HTTP-Referer": "https://github.com/your-repo/smart-question-parser",
                    "X-Title": "Smart Question Parser"
                }
            )
            
            content = response.choices[0].message.content
            return self.parse_response(content)
        
        except Exception as e:
            print(f"OpenRouter API调用失败: {e}")
            raise


class GeminiService(AIService):
    """Google Gemini服务"""
    
    def __init__(self, api_key: str, model_name: str = "gemini-pro", max_tokens: int = 4000):
        super().__init__(api_key, model_name, max_tokens)
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name)
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def parse_questions_with_remaining(self, text: str, 
                                           context: Optional[str] = None) -> Tuple[List[Question], str]:
        """使用Gemini API解析试题，返回剩余文本"""
        prompt = self.create_prompt(text, context)
        
        try:
            response = await self.model.generate_content_async(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=self.max_tokens,
                    temperature=0.3
                )
            )
            
            return self.parse_response(response.text)
        
        except Exception as e:
            print(f"Gemini API调用失败: {e}")
            raise
    
    # 保留原有方法以兼容
    async def parse_questions(self, text: str, context: Optional[str] = None) -> List[Question]:
        """使用Gemini API解析试题（兼容旧接口）"""
        questions, _ = await self.parse_questions_with_remaining(text, context)
        return questions


class AIServiceFactory:
    """AI服务工厂"""
    
    @staticmethod
    def create_service(provider: str, api_key: str, model_name: Optional[str] = None) -> AIService:
        """
        创建AI服务实例
        
        Args:
            provider: 服务提供商（openai, deepseek, gemini, openrouter）
            api_key: API密钥
            model_name: 模型名称
            
        Returns:
            AI服务实例
        """
        if provider.lower() == "openai":
            return OpenAIService(api_key, model_name or "gpt-3.5-turbo")
        elif provider.lower() == "deepseek":
            return DeepSeekService(api_key, model_name or "deepseek-chat")
        elif provider.lower() == "gemini":
            return GeminiService(api_key, model_name or "gemini-pro")
        elif provider.lower() == "openrouter":
            return OpenRouterService(api_key, model_name or "openai/gpt-3.5-turbo", max_tokens=4000)
        else:
            raise ValueError(f"不支持的AI服务提供商: {provider}") 