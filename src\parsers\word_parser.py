"""Word文档解析器"""
from docx import Document
from .base_parser import BaseParser


class WordParser(BaseParser):
    """Word文档（.docx）解析器"""
    
    def extract_text(self, file_path: str) -> str:
        """
        从Word文档中提取文本内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            提取的文本内容
        """
        doc = Document(file_path)
        text = []
        
        # 提取段落文本
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text.append(paragraph.text.strip())
        
        # 提取表格文本
        for table in doc.tables:
            table_text = self._extract_table_text(table)
            if table_text:
                text.append(table_text)
        
        return "\n\n".join(text)
    
    def _extract_table_text(self, table) -> str:
        """提取表格文本"""
        table_text = []
        
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip()
                if cell_text:
                    row_text.append(cell_text)
            
            if row_text:
                table_text.append(" | ".join(row_text))
        
        return "\n".join(table_text) if table_text else ""
    
    def extract_text_with_formatting(self, file_path: str) -> str:
        """
        提取带格式信息的文本（包括标题层级等）
        
        Args:
            file_path: 文件路径
            
        Returns:
            带格式信息的文本内容
        """
        doc = Document(file_path)
        text = []
        
        for paragraph in doc.paragraphs:
            if not paragraph.text.strip():
                continue
            
            # 检查是否是标题
            if paragraph.style.name.startswith('Heading'):
                level = paragraph.style.name[-1] if paragraph.style.name[-1].isdigit() else '1'
                prefix = '#' * int(level)
                text.append(f"{prefix} {paragraph.text.strip()}")
            else:
                text.append(paragraph.text.strip())
        
        # 提取表格
        for i, table in enumerate(doc.tables):
            text.append(f"\n[表格 {i+1}]")
            table_text = self._extract_table_text(table)
            if table_text:
                text.append(table_text)
        
        return "\n\n".join(text) 