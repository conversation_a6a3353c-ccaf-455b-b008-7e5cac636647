"""PDF文件解析器"""
import pdfplumber
import PyPDF2
from typing import Optional
from .base_parser import BaseParser


class PDFParser(BaseParser):
    """PDF文件解析器"""
    
    def extract_text(self, file_path: str) -> str:
        """
        从PDF文件中提取文本内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            提取的文本内容
        """
        text = ""
        
        # 首先尝试使用pdfplumber（效果通常更好）
        try:
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n\n"
        except Exception as e:
            # 如果pdfplumber失败，尝试使用PyPDF2
            try:
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page_num in range(len(pdf_reader.pages)):
                        page = pdf_reader.pages[page_num]
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n\n"
            except Exception as e2:
                raise Exception(f"无法解析PDF文件: {str(e)}, {str(e2)}")
        
        return text.strip()
    
    def extract_text_with_layout(self, file_path: str) -> str:
        """
        提取保留布局的文本（适用于表格等结构化内容）
        
        Args:
            file_path: 文件路径
            
        Returns:
            保留布局的文本内容
        """
        text = ""
        
        try:
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    # 提取表格
                    tables = page.extract_tables()
                    for table in tables:
                        for row in table:
                            # 将None替换为空字符串
                            row = [cell if cell else "" for cell in row]
                            text += " | ".join(row) + "\n"
                        text += "\n"
                    
                    # 提取非表格文本
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n\n"
        except Exception as e:
            # 如果失败，回退到普通提取
            return self.extract_text(file_path)
        
        return text.strip() 