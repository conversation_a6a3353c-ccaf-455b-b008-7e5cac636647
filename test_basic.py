"""基本功能测试脚本"""
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    try:
        from src.models.question import Question, QuestionType, Choice
        print("✓ 数据模型导入成功")
        
        from src.parsers.text_parser import TextParser
        from src.parsers.pdf_parser import PDFParser
        print("✓ 文档解析器导入成功")
        
        from src.services.ai_service import AIServiceFactory
        print("✓ AI服务导入成功")
        
        from src.services.question_parser_service import QuestionParserService
        print("✓ 解析服务导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False


def test_question_model():
    """测试试题模型"""
    print("\n测试试题模型...")
    try:
        from src.models.question import Question, QuestionType, Choice
        
        # 创建一个选择题
        question = Question(
            type=QuestionType.SINGLE_CHOICE,
            stem="Python中用于定义函数的关键字是？",
            choices=[
                Choice(label="A", content="function"),
                Choice(label="B", content="def"),
                Choice(label="C", content="func"),
                Choice(label="D", content="define")
            ],
            answer="B",
            analysis="def是Python中定义函数的关键字",
            score=5
        )
        
        print("✓ 试题对象创建成功")
        print(f"  题型: {question.type}")
        print(f"  题干: {question.stem}")
        print(f"  选项数: {len(question.choices)}")
        print(f"  答案: {question.answer}")
        
        # 测试JSON序列化
        json_data = question.dict()
        print("✓ JSON序列化成功")
        
        return True
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


def test_text_parser():
    """测试文本解析器"""
    print("\n测试文本解析器...")
    try:
        from src.parsers.text_parser import TextParser
        
        parser = TextParser(chunk_size=100)
        
        # 测试文本
        test_text = """这是第一段测试文本。
        
这是第二段测试文本，内容比较长，需要测试分块功能是否正常工作。
        
这是第三段测试文本。"""
        
        chunks = parser.split_text_into_chunks(test_text)
        print(f"✓ 文本分块成功，共{len(chunks)}个块")
        
        for i, chunk in enumerate(chunks):
            print(f"  块{i+1}: {chunk['tokens']} tokens")
        
        return True
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


def test_file_parsers():
    """测试文件解析器是否可以实例化"""
    print("\n测试文件解析器...")
    try:
        from src.parsers.text_parser import TextParser
        from src.parsers.pdf_parser import PDFParser
        from src.parsers.word_parser import WordParser
        from src.parsers.excel_parser import ExcelParser
        
        parsers = {
            'txt': TextParser(),
            'pdf': PDFParser(),
            'docx': WordParser(),
            'xlsx': ExcelParser()
        }
        
        print(f"✓ 成功创建{len(parsers)}种文件解析器")
        for ext, parser in parsers.items():
            print(f"  - {ext}: {parser.__class__.__name__}")
        
        return True
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


if __name__ == "__main__":
    print("=" * 50)
    print("智能试题解析器 - 基本功能测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_question_model,
        test_text_parser,
        test_file_parsers
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        if test():
            passed += 1
        else:
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}个通过, {failed}个失败")
    
    if failed == 0:
        print("✓ 所有测试通过！")
        print("\n下一步:")
        print("1. 配置.env文件中的API密钥")
        print("2. 运行 'python run.py' 启动服务")
        print("3. 访问 http://localhost:8000 使用Web界面")
    else:
        print("✗ 部分测试失败，请检查错误信息") 