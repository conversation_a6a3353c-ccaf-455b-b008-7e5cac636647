"""Excel文件解析器"""
import pandas as pd
from openpyxl import load_workbook
from .base_parser import BaseParser


class ExcelParser(BaseParser):
    """Excel文件（.xlsx, .xls）解析器"""
    
    def extract_text(self, file_path: str) -> str:
        """
        从Excel文件中提取文本内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            提取的文本内容
        """
        text = []
        
        try:
            # 使用pandas读取所有sheet
            excel_file = pd.ExcelFile(file_path)
            
            for sheet_name in excel_file.sheet_names:
                text.append(f"## Sheet: {sheet_name}\n")
                
                # 读取sheet数据
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                # 将DataFrame转换为文本
                if not df.empty:
                    # 转换为markdown表格格式
                    table_text = df.to_markdown(index=False)
                    text.append(table_text)
                else:
                    text.append("(空表)")
                
                text.append("\n")
        
        except Exception as e:
            # 如果pandas失败，尝试使用openpyxl
            try:
                wb = load_workbook(file_path, read_only=True)
                
                for sheet_name in wb.sheetnames:
                    text.append(f"## Sheet: {sheet_name}\n")
                    sheet = wb[sheet_name]
                    
                    sheet_text = self._extract_sheet_text(sheet)
                    if sheet_text:
                        text.append(sheet_text)
                    else:
                        text.append("(空表)")
                    
                    text.append("\n")
                
                wb.close()
            
            except Exception as e2:
                raise Exception(f"无法解析Excel文件: {str(e)}, {str(e2)}")
        
        return "\n".join(text).strip()
    
    def _extract_sheet_text(self, sheet) -> str:
        """从工作表中提取文本"""
        rows = []
        
        for row in sheet.iter_rows(values_only=True):
            # 过滤掉全是None的行
            if any(cell is not None for cell in row):
                # 将None替换为空字符串
                row_values = [str(cell) if cell is not None else "" for cell in row]
                rows.append(" | ".join(row_values))
        
        return "\n".join(rows) if rows else ""
    
    def extract_text_structured(self, file_path: str) -> dict:
        """
        提取结构化的Excel数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            包含所有sheet数据的字典
        """
        structured_data = {}
        
        excel_file = pd.ExcelFile(file_path)
        
        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 将DataFrame转换为字典列表
            sheet_data = {
                'name': sheet_name,
                'columns': df.columns.tolist(),
                'data': df.to_dict('records'),
                'shape': df.shape
            }
            
            structured_data[sheet_name] = sheet_data
        
        return structured_data 