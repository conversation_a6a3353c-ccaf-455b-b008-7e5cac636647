"""
智能试题解析器使用示例
"""
import requests
import json
import time





def parse_text_example():
    """文本解析示例"""
    base_url = "http://localhost:8000"
    
    # 示例试题文本
    text = """
第一部分：选择题（每题5分）

1. Python中用于定义函数的关键字是？
A. function
B. def
C. func
D. define

答案：B

2. 以下哪个不是Python的数据类型？
A. list
B. tuple
C. array
D. dict

答案：C
解析：array不是Python的内置数据类型，需要导入numpy库才能使用。

第二部分：填空题（每题10分）

3. Python中，使用____关键字来导入模块。

答案：import

第三部分：简答题（每题20分）

4. 请简述Python中列表(list)和元组(tuple)的区别。

答案：主要区别包括：
1. 可变性：列表是可变的，元组是不可变的
2. 性能：元组的访问速度比列表快
3. 用途：列表用于需要修改的数据集合，元组用于不需要修改的数据
"""
    
    config = {
        'text': text,
        'ai_provider': 'deepseek',
        'api_key': 'your_api_key_here'  # 替换为实际的API密钥
    }
    
    print("正在解析文本中的试题...")
    
    response = requests.post(
        f"{base_url}/api/parse/text",
        data=config
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"\n解析成功！找到 {result['total_questions']} 道试题")
        
        # 显示解析结果
        for i, question in enumerate(result['questions']):
            print(f"\n题目 {i+1}:")
            print(f"  类型: {question['type']}")
            print(f"  题干: {question['stem']}")
            if question.get('choices'):
                print("  选项:")
                for choice in question['choices']:
                    print(f"    {choice['label']}. {choice['content']}")
            if question.get('answer'):
                print(f"  答案: {question['answer']}")
            if question.get('analysis'):
                print(f"  解析: {question['analysis']}")
            if question.get('score'):
                print(f"  分值: {question['score']}")
    else:
        print(f"解析失败: {response.text}")


def check_providers():
    """检查可用的AI服务提供商"""
    base_url = "http://localhost:8000"
    
    response = requests.get(f"{base_url}/api/providers")
    if response.status_code == 200:
        providers = response.json()['providers']
        print("可用的AI服务提供商:")
        for provider in providers:
            print(f"\n- {provider['name']} (ID: {provider['id']})")
            print(f"  支持的模型: {', '.join(provider['models'])}")
            print(f"  默认模型: {provider['default_model']}")
    else:
        print("获取提供商列表失败")


if __name__ == "__main__":
    import sys
    
    print("智能试题解析器示例")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "text":
            parse_text_example()
        elif sys.argv[1] == "providers":
            check_providers()
        else:
            print("未知命令")
    else:
        print("\n使用方法:")
        print("  python example_usage.py text      - 解析文本示例")
        print("  python example_usage.py providers - 查看可用的AI提供商")
        print("\n请先确保服务已启动 (python run.py)")
        print("并在脚本中设置正确的API密钥") 