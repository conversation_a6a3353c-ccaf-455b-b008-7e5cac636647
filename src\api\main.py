"""FastAPI主应用"""
import os
import shutil
from pathlib import Path
from typing import List, Optional
from datetime import datetime
import uuid

from fastapi import FastAPI, File, UploadFile, HTTPException, Form, BackgroundTasks
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
from dotenv import load_dotenv

from ..services.question_parser_service import QuestionParserService
from ..models.question import Question, QuestionSet

# 加载环境变量
load_dotenv()

# 创建FastAPI应用
app = FastAPI(
    title="智能试题解析器",
    description="使用AI大模型解析各种格式的试题文档",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置上传目录
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)
RESULTS_DIR = Path("results")
RESULTS_DIR.mkdir(exist_ok=True)

# 全局解析服务实例（可以根据需要动态创建）
parser_service = None

# 任务存储（实际应用中应使用数据库）
tasks = {}


async def save_upload_file(upload_file: UploadFile) -> str:
    """保存上传的文件并返回文件路径"""
    # 生成唯一文件名
    file_extension = Path(upload_file.filename).suffix.lower()
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = UPLOAD_DIR / unique_filename
    
    # 保存文件
    with open(file_path, "wb") as buffer:
        content = await upload_file.read()
        buffer.write(content)
    
    return str(file_path)


class ParseRequest(BaseModel):
    """解析请求模型"""
    ai_provider: str = Field("deepseek", description="AI服务提供商")
    api_key: str = Field(..., description="API密钥")
    model_name: Optional[str] = Field(None, description="模型名称")
    chunk_size: int = Field(2000, description="文本块大小")
    max_concurrent_requests: int = Field(5, description="最大并发请求数")


class TaskStatus(BaseModel):
    """任务状态模型"""
    task_id: str
    status: str
    message: str
    progress: float
    result: Optional[dict] = None
    error: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class QuestionSetResponse(BaseModel):
    """试题集响应模型"""
    success: bool = Field(True, description="是否成功")
    data: Optional[QuestionSet] = Field(None, description="试题集数据")
    message: str = Field("", description="响应消息")
    error: Optional[str] = Field(None, description="错误信息")


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "智能试题解析器API",
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.post("/api/parse/file")
async def parse_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    ai_provider: str = Form("deepseek"),
    api_key: str = Form(...),
    model_name: Optional[str] = Form(None),
    chunk_size: int = Form(2000),
    max_concurrent_requests: int = Form(5)
):
    """
    上传并解析试题文件
    
    支持的文件格式：txt, pdf, docx, xlsx, xls
    """
    # 验证文件大小
    if file.size > 104857600:  # 100MB
        raise HTTPException(status_code=413, detail="文件太大，最大支持100MB")
    
    # 验证文件格式
    file_extension = Path(file.filename).suffix.lower()
    supported_extensions = ['.txt', '.pdf', '.docx', '.doc', '.xlsx', '.xls']
    if file_extension not in supported_extensions:
        raise HTTPException(
            status_code=400, 
            detail=f"不支持的文件格式。支持的格式：{', '.join(supported_extensions)}"
        )
    
    # 保存上传的文件
    task_id = str(uuid.uuid4())
    file_path = UPLOAD_DIR / f"{task_id}{file_extension}"
    
    try:
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件保存失败: {str(e)}")
    
    # 创建任务
    task = TaskStatus(
        task_id=task_id,
        status="pending",
        message="任务已创建",
        progress=0,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    tasks[task_id] = task
    
    # 在后台执行解析任务
    background_tasks.add_task(
        process_file_task,
        task_id,
        file_path,
        ai_provider,
        api_key,
        model_name,
        chunk_size,
        max_concurrent_requests
    )
    
    return {
        "task_id": task_id,
        "message": "文件上传成功，正在处理...",
        "status_url": f"/api/task/{task_id}"
    }


async def process_file_task(
    task_id: str,
    file_path: Path,
    ai_provider: str,
    api_key: str,
    model_name: Optional[str],
    chunk_size: int,
    max_concurrent_requests: int
):
    """后台处理文件解析任务"""
    task = tasks[task_id]
    
    try:
        # 更新任务状态
        task.status = "processing"
        task.message = "正在初始化解析服务..."
        task.updated_at = datetime.now()
        
        # 创建解析服务
        service = QuestionParserService(
            ai_provider=ai_provider,
            api_key=api_key,
            model_name=model_name,
            chunk_size=chunk_size,
            max_concurrent_requests=max_concurrent_requests
        )
        
        # 定义进度回调
        def update_progress(info):
            task.status = info.get('status', 'processing')
            task.message = info.get('message', '')
            task.progress = info.get('progress', 0)
            task.updated_at = datetime.now()
        
        # 使用动态分块解析文件
        question_set = await service.parse_file_dynamic(str(file_path), callback=update_progress)
        
        # 保存结果
        result_path = RESULTS_DIR / f"{task_id}.json"
        with open(result_path, "w", encoding="utf-8") as f:
            import json
            # 使用模型的JSON序列化方法
            result_data = json.loads(question_set.json())
            json.dump(result_data, f, ensure_ascii=False, indent=2)
        
        # 更新任务状态
        task.status = "completed"
        task.message = f"解析完成，共找到{len(question_set.questions)}道试题"
        task.progress = 100
        task.result = {
            "total_questions": len(question_set.questions),
            "question_types": _count_question_types(question_set.questions),
            "total_score": question_set.total_score,
            "result_url": f"/api/results/{task_id}"
        }
        task.updated_at = datetime.now()
        
    except Exception as e:
        task.status = "failed"
        task.message = "解析失败"
        task.error = str(e)
        task.updated_at = datetime.now()
    
    finally:
        # 清理上传的文件
        try:
            os.remove(file_path)
        except:
            pass


@app.get("/api/task/{task_id}")
async def get_task_status(task_id: str):
    """获取任务状态"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return tasks[task_id]


@app.get("/api/results/{task_id}")
async def get_results(task_id: str):
    """获取解析结果"""
    result_path = RESULTS_DIR / f"{task_id}.json"
    
    if not result_path.exists():
        raise HTTPException(status_code=404, detail="结果文件不存在")
    
    try:
        with open(result_path, "r", encoding="utf-8") as f:
            import json
            return json.load(f)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取结果失败: {str(e)}")


@app.post("/api/parse/text")
async def parse_text(
    text: str = Form(...),
    ai_provider: str = Form("deepseek"),
    api_key: str = Form(...),
    model_name: Optional[str] = Form(None)
):
    """
    直接解析文本中的试题
    """
    try:
        import json
        # 创建解析服务
        service = QuestionParserService(
            ai_provider=ai_provider,
            api_key=api_key,
            model_name=model_name
        )
        
        # 解析文本
        question_set = await service.parse_text(text)
        
        return {
            "success": True,
            "total_questions": len(question_set.questions),
            "questions": [json.loads(q.json()) for q in question_set.questions]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"解析失败: {str(e)}")


@app.get("/api/providers")
async def get_providers():
    """获取支持的AI服务提供商"""
    return {
        "providers": [
            {
                "id": "deepseek",
                "name": "DeepSeek",
                "models": ["deepseek-chat", "deepseek-coder"],
                "default_model": "deepseek-chat"
            },
            {
                "id": "openai",
                "name": "OpenAI",
                "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo-preview"],
                "default_model": "gpt-3.5-turbo"
            },
            {
                "id": "gemini",
                "name": "Google Gemini",
                "models": ["gemini-pro", "gemini-pro-vision"],
                "default_model": "gemini-pro"
            },
            {
                "id": "openrouter",
                "name": "OpenRouter",
                "models": [
                    "openai/gpt-3.5-turbo",
                    "openai/gpt-4",
                    "openai/gpt-4-turbo",
                    "anthropic/claude-3-haiku",
                    "anthropic/claude-3-sonnet",
                    "anthropic/claude-3-opus",
                    "google/gemini-pro",
                    "google/gemini-2.0-flash-exp:free",
                    "google/gemini-2.5-flash-lite-preview-06-17",
                    "google/gemini-2.5-pro",
                    "google/gemini-2.5-flash",
                    "meta-llama/llama-2-70b-chat",
                    "mistralai/mistral-7b-instruct",
                    "cohere/command-r-plus"
                ],
                "default_model": "google/gemini-2.5-flash-lite-preview-06-17"
            }
        ]
    }


def _count_question_types(questions: List[Question]) -> dict:
    """统计试题类型"""
    type_counts = {}
    for q in questions:
        type_name = q.type.value
        type_counts[type_name] = type_counts.get(type_name, 0) + 1
    return type_counts


# 挂载静态文件目录（用于前端）
if Path("static").exists():
    app.mount("/", StaticFiles(directory="static", html=True), name="static")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.api.main:app",
        host=os.getenv("SERVER_HOST", "0.0.0.0"),
        port=int(os.getenv("SERVER_PORT", 8000)),
        reload=True
    ) 