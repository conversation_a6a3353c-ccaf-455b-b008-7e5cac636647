#!/usr/bin/env python3
"""测试试题解析修复效果"""

import time
import requests
import json

def test_parsing_fixes():
    """测试解析修复效果"""
    server_url = "http://localhost:8000"
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(5)
    
    # 测试数据 - 包含各种类型的试题
    test_text = """
1. Python中，以下哪个选项可以正确打印"Hello, World!"？
A. A. print("Hello, World!")
B. B. printf("Hello, World!")
C. C. echo "Hello, World!"
D. D. console.log("Hello, World!")
答案：A
解析：Python的打印函数为print()，其他选项分别为C语言、PHP和JavaScript的语法

11. 设计多选题时，降低"单选率"（只选1项）的方法包括？
选项：
A. A. 控制选项计算量
B. B. 设置干扰关系的选项
C. C. 梯度平缓的选项难度
D. D. 增加干扰项数量
答案：A、B、C
解析：计算量大或难度跳跃易导致学生"选一项就跑"，并列关系和梯度设计可改善

16. 公历年份能被4整除的均为闰年。
答案：错误
解析：整百年份需被400整除（如1900年不是闰年）
"""
    
    try:
        # 测试文本解析API
        data = {
            'text': test_text,
            'ai_provider': 'deepseek',
            'api_key': 'your-api-key-here',  # 需要替换为实际的API密钥
            'model_name': 'deepseek-chat'
        }
        
        print("发送解析请求...")
        response = requests.post(f"{server_url}/api/parse/text", data=data)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 解析成功！")
            print(f"总题数: {result['total_questions']}")
            
            # 检查每道题的解析结果
            for i, question in enumerate(result['questions'], 1):
                print(f"\n题目 {i}:")
                print(f"  类型: {question['type']}")
                print(f"  题干: {question['stem'][:50]}...")
                
                if question.get('choices'):
                    print("  选项:")
                    for choice in question['choices']:
                        print(f"    {choice['label']}. {choice['content'][:30]}...")
                
                print(f"  答案: {question.get('answer', 'N/A')}")
            
            # 验证修复效果
            print("\n=== 修复效果验证 ===")
            questions = result['questions']
            
            # 检查第一题的选项是否还有重复标签
            if questions:
                first_q = questions[0]
                if first_q.get('choices'):
                    for choice in first_q['choices']:
                        if choice['content'].startswith(choice['label']):
                            print("❌ 选项标签仍然重复")
                            break
                    else:
                        print("✅ 选项标签重复问题已修复")
            
            # 检查是否正确识别了多选题和判断题
            types_found = [q['type'] for q in questions]
            if 'multiple_choice' in types_found:
                print("✅ 多选题识别正常")
            else:
                print("❌ 多选题识别仍有问题")
            
            if 'true_false' in types_found:
                print("✅ 判断题识别正常")
            else:
                print("❌ 判断题识别仍有问题")
        
        else:
            print(f"❌ 解析失败: {response.status_code}")
            print(f"错误信息: {response.text}")
    
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_parsing_fixes() 